package com.chervon.technology.domain.vo.rule.log;

import com.chervon.common.mybatis.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 日志规则查询结果Vo
 * <AUTHOR>
 * @since 2024-11-06 10:26
 **/
@Data
public class LogRuleVo implements Serializable {
    /**
     * 日志规则id
     */
    private Long id;
    /**
     * 日志模板Id
     */
    private Long logTemplateId;
    /**
     * 日志标题
     */
    private String title;
    /**
     * 是否启用 1启用 0禁用
     */
    @ApiModelProperty("是否启用 1启用 0禁用")
    private Integer enabled;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 更新人
     */
    private String updateBy;
    /**
     * 创建时间
     */
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;

}
