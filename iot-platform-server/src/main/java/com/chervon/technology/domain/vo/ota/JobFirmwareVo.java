package com.chervon.technology.domain.vo.ota;

import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 */
@Data
@ApiModel("升级任务jobFirmware列表")
public class JobFirmwareVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 任务ID
     **/
    @ApiModelProperty("任务ID")
    private Long jobId;
    /**
     * packageId
     **/
    @ApiModelProperty("升级包id")
    private Long packageId;
    /**
     * 升级包类型：FULL_PACKAGE，DELTA_PACKAGE
     **/
    @ApiModelProperty("升级包类型")
    private String packageType;

    @ApiModelProperty("显示版本号")
    private String displayVersion;
    /**
     * 升级包版本号
     **/
    @ApiModelProperty("升级包版本号")
    private String packageVersion;

    /**
     * 最低兼容版本号
     **/
    @ApiModelProperty("最低兼容版本号")
    private String minimumVersion;

    /**
     * 创建时间
     **/
    @ApiModelProperty("创建时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    /**
     * 固件包名称
     **/
    private String packageName;
    /**
     * 文件体积，单位byte
     **/
    private Long size;

    /**
     * 固件包标识，用于获取最新的预签名url
     **/
    private String packageKey;

    /**
     * 固件包hash值，sha1算法，用于校验固件包完整性
     **/
    private String hash;
}
