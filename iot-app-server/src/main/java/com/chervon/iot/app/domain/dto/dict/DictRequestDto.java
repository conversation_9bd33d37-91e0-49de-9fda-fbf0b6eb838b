package com.chervon.iot.app.domain.dto.dict;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> 2025/4/10
 */
@Data
public class DictRequestDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "字典key值")
    private String dicKey;

    @ApiModelProperty(value = "多语言语种code:如zh,en,fr,de等")
    private String langCode;

    @ApiModelProperty(value = "允许字典模糊匹配的描述值")
    private String content;
}
