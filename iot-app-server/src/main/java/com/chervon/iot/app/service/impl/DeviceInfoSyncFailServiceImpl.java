package com.chervon.iot.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.iot.app.domain.dataobject.DeviceInfoSyncFail;
import com.chervon.iot.app.domain.enums.DeviceInfoSyncDealStatusEnum;
import com.chervon.iot.app.mapper.DeviceInfoSyncFailMapper;
import com.chervon.iot.app.service.DeviceInfoService;
import com.chervon.iot.app.service.DeviceInfoSyncFailService;
import com.chervon.usercenter.api.service.SaleForceService;
import com.chervon.usercenter.api.service.UserCommandService;
import com.chervon.usercenter.api.service.UserQueryService;
import com.chervon.usercenter.api.vo.sf.SfUserRecord;
import com.chervon.usercenter.api.vo.sf.SfWarrantyRecord;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/7
 * @description
 */
@Service
@Slf4j
public class DeviceInfoSyncFailServiceImpl extends
        ServiceImpl<DeviceInfoSyncFailMapper, DeviceInfoSyncFail> implements DeviceInfoSyncFailService {
    private static final int SN_LEN_LIMIT = 128;
    private static final int MSG_LEN_LIMIT = 255;

    @DubboReference(timeout=20000, group = "${sf.direction}")
    private SaleForceService saleForceService;
    @DubboReference
    private UserCommandService userCommandService;
    @DubboReference
    private UserQueryService userQueryService;

    @Resource
    private DeviceInfoService deviceInfoService;

    @Override
    public List<DeviceInfoSyncFail> findNotDealSyncFail(int limit){
        return baseMapper.selectList(new LambdaQueryWrapper<DeviceInfoSyncFail>()
                .in(DeviceInfoSyncFail::getDealStatus, DeviceInfoSyncDealStatusEnum.needDealStatus())
                .last("LIMIT "+limit));
    }

    @Override
    public void batchUpdateStatus(List<Long> idList, int dealStatus){
    update(new LambdaUpdateWrapper<DeviceInfoSyncFail>()
            .set(DeviceInfoSyncFail::getDealStatus,dealStatus)
            .in(DeviceInfoSyncFail::getId,idList));
    }

    @Override
    public void dealSyncFailList(List<DeviceInfoSyncFail> notDealSyncFail){
        Map<Long, Integer> updateStatusMap = new HashMap<>(notDealSyncFail.size());
        for (DeviceInfoSyncFail deviceInfoSyncFail : notDealSyncFail) {
            //设置默认
            updateStatusMap.put(deviceInfoSyncFail.getId(),DeviceInfoSyncDealStatusEnum.DEAL_FAIL.getValue());
            retrySync(deviceInfoSyncFail,updateStatusMap);
        }
        //将需要更新的状态
        Map<Integer, List<Long>> statusListMap = updateStatusMap.entrySet()
                .stream().collect(
                        Collectors.groupingBy(Map.Entry::getValue,
                                Collectors.mapping(Map.Entry::getKey,
                                        Collectors.toList())));
        for (Map.Entry<Integer, List<Long>> listEntry : statusListMap.entrySet()) {
            this.batchUpdateStatus(listEntry.getValue(), listEntry.getKey());
        }
    }
    private void retrySync(DeviceInfoSyncFail deviceInfoSyncFail,Map<Long, Integer> updateStatusMap){
        String sfUserId = deviceInfoSyncFail.getSfUserId();
        Long syncFailId = deviceInfoSyncFail.getId();
        Long userId = userQueryService.getUserIdBySfUserId(sfUserId);
        //如果IOT还是不存在该用户，则再次同步用户
        if(userId==null){
            SfUserRecord sfUser = saleForceService.getSfUserBySfUserId(sfUserId);
            //如果账号在CRM不存在密码，则
            if (sfUser != null) {
                userCommandService.syncUserFromSf(Lists.newArrayList(sfUser));
            }else {
                updateStatusMap.put(syncFailId,DeviceInfoSyncDealStatusEnum.DEAL_IGNORE.getValue());
                return;
            }
        }
        String sn = deviceInfoSyncFail.getSn();
        //如果质保数据已存在缓存，说明已同步成功
        boolean b = deviceInfoService.existsSyncTemp(sn, sfUserId);
        if(b){
            updateStatusMap.put(syncFailId,DeviceInfoSyncDealStatusEnum.DEAL_SUCCESS.getValue());
        }else {
            List<SfWarrantyRecord> sfWarrantyRecord = saleForceService.getWarrantyBySn(sn);
            if(sfWarrantyRecord == null) {
                log.info("no warranty found, sn:{}", sn);
                return;
            }
            try {
                deviceInfoService.syncDeviceFromCrm(sfWarrantyRecord);
            } catch (Exception e) {
                log.warn("XxlJobTask#syncOneSfWarranty, error: ", e);
            }
            //若质保数据重试同步成功，则将异常表置为处理成功
            boolean syncTemp = deviceInfoService.existsSyncTemp(sn, sfUserId);
            if(syncTemp){
                updateStatusMap.put(syncFailId,DeviceInfoSyncDealStatusEnum.DEAL_SUCCESS.getValue());
            }
        }
    }

    /**
     * 保存同步异常信息
     */
    @Override
    public void saveSyncFailInfo(String sn, String sfUserId, String msg, Integer dealStatus) {
        DeviceInfoSyncFail deviceInfoSyncFail = baseMapper
                .selectOne(new LambdaQueryWrapper<DeviceInfoSyncFail>()
                        .select(DeviceInfoSyncFail::getId, DeviceInfoSyncFail::getDealStatus)
                        .eq(DeviceInfoSyncFail::getSn, sn).eq(DeviceInfoSyncFail::getSfUserId, sfUserId)
                        //获取最后一条同步失败记录
                        .orderByDesc(DeviceInfoSyncFail::getId)
                        .last("LIMIT 1"));
        //如果不存在失败记录，或者失败记录为已处理状态，则新增记录
        if (deviceInfoSyncFail == null ||
                DeviceInfoSyncDealStatusEnum.isDealSuccess(deviceInfoSyncFail.getDealStatus())) {
            DeviceInfoSyncFail entity = new DeviceInfoSyncFail();
            entity.setSn(sn);
            entity.setDealStatus(dealStatus);
            if (sn.length() > SN_LEN_LIMIT) {
                entity.setSn(sn.substring(0, SN_LEN_LIMIT));
            }
            entity.setSfUserId(sfUserId);
            if (msg != null && msg.length() > MSG_LEN_LIMIT) {
                entity.setMsg(msg.substring(0, MSG_LEN_LIMIT));
            } else {
                entity.setMsg(msg);
            }
            try {
                baseMapper.insert(entity);
            } catch (Exception e) {
                log.warn("saveSyncFailInfo error, sn:{}, sfUserId:{}, ", sn, sfUserId, e);
            }
        }
    }

}
