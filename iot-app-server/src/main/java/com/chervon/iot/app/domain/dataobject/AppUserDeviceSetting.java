package com.chervon.iot.app.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 用户设备设置表(app_user_device_setting)实体类
 *
 * <AUTHOR>
 * @since 2025-04-02 18:17:56
 * @description 
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("app_user_device_setting")
public class AppUserDeviceSetting extends BaseDo {
    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    private Long userId;
    /**
     * 设备Id
     */
    private String deviceId;
    /**
     * 是否已完成远程控制练习，0:未完成，1:已完成
     */
    private Integer remoteControlPractice;

    /**
     * 当前完成的引导页节点
     */
    private Integer guideStep;
}