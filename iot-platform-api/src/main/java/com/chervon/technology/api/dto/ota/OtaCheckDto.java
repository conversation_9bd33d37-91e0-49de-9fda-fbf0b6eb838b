package com.chervon.technology.api.dto.ota;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 检查ota是否有新任务请求参数
 */
@ApiModel("检查ota是否有新任务请求参数")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class OtaCheckDto implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 设备id
	 **/
	@ApiModelProperty("设备id")
	private String deviceId;

	/**
	 * 设备id列表，批量查询接口使用
	 **/
	@ApiModelProperty("设备id列表，批量查询接口使用")
	private List<String> listDeviceId;
	/**
	 * 是否为单MCU升级：true：单MCU升级； false:整合升级(适用R设备传false)
	 *  R设备传false
	 **/
	@ApiModelProperty("是否为单MCU升级：true：单MCU升级； false:整合升级(适用R设备传false)")
	private Boolean singleMcu;
}
