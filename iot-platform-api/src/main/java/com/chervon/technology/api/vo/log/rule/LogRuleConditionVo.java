package com.chervon.technology.api.vo.log.rule;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 日志规则条件配置
 * <AUTHOR>
 * @since 2024-11-04 14:26
 **/
@Data
public class LogRuleConditionVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 分组Id（相同分组id是与关系，不同分组id之间是或关系）
     */
    @ApiModelProperty("分组Id")
    private String groupId;
    /**
     * 触发条件类型：0属性 1事件 2服务
     */
    @ApiModelProperty("触发条件类型：0属性 1事件  2服务")
    private Integer conditionType;
    /**
     * 物模型Id，可能为属性、事件、服务
     * 与下面判断规则,参数值一起传给中台,生成SQL语句
     */
    @ApiModelProperty("物模型功能key")
    private String identifierId;
    /**
     * 当物模型为属性时的子事件故障编码
     */
    @ApiModelProperty("当物模型为属性时的子事件故障编码")
    private String eventCode;
    /**
     * 比较符号
     */
    @ApiModelProperty("比较符号")
    private String symbol;
    /**
     * 参数值
     */
    @ApiModelProperty("参数值")
    private String value;
    /**
     * 数据类型: int(原生), float(原生), double(原生), date(String类型UTC毫秒),
     * bool(0或1的int类型), enum(int类型), string(字符串), raw(透传), array(数组)
     */
    @ApiModelProperty("数据类型: int(原生), float(原生), double(原生), date(String类型UTC毫秒),bool(0或1的int类型), enum(int类型), string(字符串), raw(透传), array(数组)")
    private String dataType;
}
